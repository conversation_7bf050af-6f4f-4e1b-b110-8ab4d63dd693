/*
 * Copyright (c) 2024, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
/*!****************************************************************************
 *  @file       idReferencing.h
 *  @brief      idReferencing Module
 *
 * 
 *  @anchor idReferencing_h
 *  # Overview
 *
 *
 *  <hr>
 ******************************************************************************/


#ifndef FOC_INCLUDE_IDREFERENCING_H_
#define FOC_INCLUDE_IDREFERENCING_H_

#include "stdint.h"
#include "fluxWeak.h"
#include "mtpa.h"

/*! @brief Id reference Flags Structure */
typedef union
{
    /*! @brief Id Ref Flags Structure */

    struct idReferencingFlags
    {
        _Bool
        /*! Enables MTPA */
            mtpaEnable:             1,
            /*! Enables Flux Weakening */
            fluxWeakeningEnable:    1,
            /*! Enables flux mode */
            fluxModeEnable:         1;

    }
    /*! Bitwise access */
    b;
    /*! Block access */
    uint16_t w;

}ID_REFERENCING_FLAGS_T;

/*! @brief Id reference Working Structure */
typedef struct
{
    FLUX_WEAK_T
    /*! Flux Weakening */
        fluxWeak;

    MTPA_T
    /*! Maximum Torque Per Ampere */
        mtpa;

    int32_t
    /*! Flux Mode */
        idRefFluxModeSet,
    /*! D-axis current reference */
        idRef;

    ID_REFERENCING_FLAGS_T
    /*! Flags */
        flags;

}ID_REF_T;

/* Id Referencing Interface Functions */

/**
 * @brief     Id Reference Init Init
 * @param[in] *pIdRef Input Pointer
 */
void idRefInit(ID_REF_T *pIdRef);
/**
 * @brief     Id Reference Low Priority Run
 * @param[in] *pIdRef Input Pointer
 */
void idRefLowPriorityRun(ID_REF_T *pIdRef);

#endif /* FOC_INCLUDE_IDREFERENCING_H_ */
