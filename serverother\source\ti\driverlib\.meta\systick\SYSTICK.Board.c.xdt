%%{
/*
 * Copyright (c) 2018-2019, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CO<PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== SYSTICK.Board.c.xdt ========
 */

    let SYSTICK = args[0];
    let content = args[1];

    /* shorthand names for some common references in template below */
    let stat = SYSTICK.$static;
    if (stat.length == 0) return;


    switch(content){
        case "Call":
            printCall();
            break;
        case "Reset":
            printReset();
            break;
        case "Power":
            printPower();
            break;
        case "GPIO":
            printGPIO();
            break;
        case "Function":
            printFunction();
            break;
        default:
            /* do nothing */
            return;
            break;
    }
%%}
%
% function printCall(){
    SYSCFG_DL_SYSTICK_init();
% }
%
% function printReset(){
% }
% function printPower(){
% }
%
% function printGPIO(){
%       /* all of these are defined in the header */
%       // No need to initialize GPIOs for SYSTICK functionality
% }
%
% /* Main Function */
% function printFunction(){
SYSCONFIG_WEAK void SYSCFG_DL_SYSTICK_init(void)
{
% if(stat.systickEnable && stat.periodEnable && stat.interruptEnable){
    /*
     * Initializes the SysTick period to `stat.calcPeriod`,
     * enables the interrupt, and starts the SysTick Timer
     */
    DL_SYSTICK_config(`stat.period`);
%} else {
% if(stat.periodEnable){
    /* Initialize the period to `stat.calcPeriod` */
    DL_SYSTICK_init(`stat.period`);
% }
% if(stat.interruptEnable){
    DL_SYSTICK_enableInterrupt();
%        if(stat.interruptPriority !== "DEFAULT"){
%               let irqnStr = "SysTick_IRQn";
    NVIC_SetPriority(`irqnStr`, `stat.interruptPriority`);
%        }
% }
% if(stat.systickEnable){
    /* Enable the SysTick and start counting */
    DL_SYSTICK_enable();
%}
%} // if(stat.systickEnable && stat.periodEnable && stat.interruptEnable)
}
%
%
% } // printFunction()
